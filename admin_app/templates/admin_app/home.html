{% extends 'base.html' %}

{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}

{% block extra_css %}
<!-- Additional Google Fonts for Display Typography -->
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* CozyWish Home Page - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Hero Section */
    .home-hero {
        background: var(--cw-gradient-hero);
        padding: 5rem 0;
        position: relative;
        overflow: hidden;
    }

    .home-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="spa-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23spa-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .home-hero .container {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-family: var(--cw-font-display);
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(47, 22, 15, 0.1);
    }

    .hero-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2.5rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Professional Search Container */
    .search-container {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
        border: 2px solid var(--cw-brand-accent);
    }

    .search-form-group {
        margin-bottom: 1.5rem;
    }

    .search-form-group:last-child {
        margin-bottom: 0;
    }

    .search-label {
        display: block;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .search-input-wrapper {
        position: relative;
    }

    .search-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        width: 100%;
        height: 3.5rem;
    }

    .search-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .search-input::placeholder {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--cw-brand-primary);
        z-index: 3;
        font-size: 1.1rem;
    }

    .location-actions {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 4;
    }

    .location-btn {
        background: none;
        border: none;
        color: var(--cw-brand-primary);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .location-btn:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .search-btn {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.75rem;
        color: white;
        padding: 1rem 2.5rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
        width: 100%;
        height: 3.5rem;
        font-size: 1.1rem;
        letter-spacing: 0.025em;
    }

    .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
    }

    .search-btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.2);
        outline: none;
    }

    .booking-count {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        margin-top: 1.5rem;
    }

    .booking-count strong {
        color: var(--cw-brand-primary);
        font-weight: 700;
    }

    /* Banner Slider Section */
    .banner-slider {
        padding: 0;
        margin: 0;
        background: white;
        position: relative;
        overflow: hidden;
        margin-top: -1rem; /* Reduce gap from hero section */
    }

    .banner-carousel {
        position: relative;
        width: 100%;
        height: 400px;
        overflow: hidden;
    }

    .banner-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.8s ease-in-out;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }

    .banner-slide.active {
        opacity: 1;
    }

    .banner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(47, 22, 15, 0.7) 0%, rgba(47, 22, 15, 0.3) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .banner-content {
        text-align: center;
        color: white;
        max-width: 600px;
        padding: 2rem;
    }

    .banner-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .banner-subtitle {
        font-size: 1.125rem;
        margin-bottom: 1.5rem;
        opacity: 0.95;
        line-height: 1.6;
    }

    .banner-cta {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        color: white;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .banner-cta:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
        text-decoration: none;
    }

    /* Banner Navigation */
    .banner-indicators {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 0.75rem;
        z-index: 10;
    }

    .banner-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .banner-indicator.active {
        background: white;
        transform: scale(1.2);
    }

    .banner-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .banner-nav:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-50%) scale(1.1);
    }

    .banner-nav.prev {
        left: 2rem;
    }

    .banner-nav.next {
        right: 2rem;
    }

    /* Section Styling */
    .popular-categories {
        padding: 3rem 0; /* Reduced padding to minimize gap */
        background: #FFF9F4;
    }

    .service-highlights {
        padding: 5rem 0;
        background: #FFF9F4; /* Changed from white to match other sections */
    }

    .section-sm {
        padding: 4rem 0;
        background: #FFF9F4;
    }

    .section-sm:nth-child(even) {
        background: white;
    }

    .section-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        text-align: center;
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        text-align: center;
        margin-bottom: 3rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Card Components */
    .card-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .card-cw:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    /* Category Cards */
    .category-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        background: var(--cw-gradient-card-subtle);
    }

    .category-icon {
        font-size: 2.5rem;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: block;
    }

    .category-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .category-count {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Service Cards */
    .service-card-highlight {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-sm);
    }

    .service-card-highlight:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        background: var(--cw-gradient-card-subtle);
    }

    .service-icon {
        font-size: 2.5rem;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: block;
    }

    .service-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-family: var(--cw-font-heading);
    }

    .service-description {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        line-height: 1.6;
    }

    /* Venue Cards */
    .service-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
        height: 100%;
    }

    .service-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
    }

    .service-card .card-img-top {
        height: 200px;
        object-fit: cover;
        border: none;
    }

    .service-card .card-body {
        padding: 1.5rem;
    }

    .service-card .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-family: var(--cw-font-heading);
    }

    .service-card .card-title a {
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    .service-card .card-title a:hover {
        color: var(--cw-brand-light);
    }

    .rating {
        margin-bottom: 0.5rem;
    }

    .rating-score {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .location {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .business-type {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-block;
    }

    /* Business Section */
    .business-section {
        padding: 5rem 0;
        background: var(--cw-gradient-card-subtle);
        position: relative;
        overflow: hidden;
    }

    .business-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="business-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23business-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .business-section .container {
        position: relative;
        z-index: 2;
    }

    .business-title {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .business-description {
        font-size: 1.125rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        color: white;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        color: var(--cw-brand-primary);
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .rating-container {
        margin-top: 2rem;
    }

    .rating-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .star-rating {
        margin-bottom: 0.75rem;
    }

    .star-rating .star {
        color: #FFD700;
        font-size: 1.25rem;
        margin-right: 0.25rem;
    }

    .review-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Dropdown Styling */
    .dropdown-menu {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 0.5rem 0;
    }

    .dropdown-item {
        color: var(--cw-neutral-700);
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
    }

    .dropdown-item:hover, .dropdown-item:focus {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .dropdown-item i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
        width: 1rem;
    }

    .dropdown-header {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .business-title {
            font-size: 2.25rem;
        }

        .category-card, .service-card-highlight {
            padding: 1.5rem 1rem;
        }

        .category-icon, .service-icon {
            font-size: 2rem;
        }

        .search-container {
            padding: 1.5rem;
        }

        .search-input {
            padding: 0.875rem 0.875rem 0.875rem 2.75rem;
            font-size: 0.9rem;
            height: 3rem;
        }

        .search-icon {
            left: 0.875rem;
            font-size: 1rem;
        }

        .search-btn {
            height: 3rem;
            font-size: 1rem;
            padding: 0.875rem 2rem;
        }

        .search-label {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 576px) {
        .hero-title {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.75rem;
        }

        .business-title {
            font-size: 1.875rem;
        }

        .category-card, .service-card-highlight {
            padding: 1.25rem 0.75rem;
        }

        .search-container {
            padding: 1rem;
        }

        .search-input {
            height: 2.75rem;
            padding: 0.75rem 0.75rem 0.75rem 2.5rem;
            font-size: 0.875rem;
        }

        .search-icon {
            left: 0.75rem;
            font-size: 0.9rem;
        }

        .search-btn {
            height: 2.75rem;
            font-size: 0.9rem;
            padding: 0.75rem 1.5rem;
        }

        .search-label {
            font-size: 0.75rem;
            margin-bottom: 0.375rem;
        }

        .search-form-group {
            margin-bottom: 1rem;
        }

        /* Banner Slider Mobile */
        .banner-carousel {
            height: 300px;
        }

        .banner-title {
            font-size: 1.875rem;
        }

        .banner-subtitle {
            font-size: 1rem;
        }

        .banner-content {
            padding: 1.5rem;
        }

        .banner-nav {
            width: 40px;
            height: 40px;
        }

        .banner-nav.prev {
            left: 1rem;
        }

        .banner-nav.next {
            right: 1rem;
        }

        .banner-indicators {
            bottom: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block hero_content %}
<!-- Hero Section -->
<section class="home-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="hero-title">Find & Book<br>Local Spa and Massage Services</h1>
                <p class="hero-subtitle">Discover top-rated wellness venues and book your perfect relaxation experience</p>

                <!-- Professional Search Form -->
                <div class="search-container">
                    <form method="GET" action="{% url 'venues_app:venue_search' %}">
                        <div class="row g-4">
                            <!-- Service Search Field -->
                            <div class="col-lg-5 col-md-6">
                                <div class="search-form-group">
                                    <label class="search-label" for="search-query">What service are you looking for?</label>
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-search search-icon"></i>
                                        {% if search_form %}
                                            {{ search_form.query }}
                                        {% else %}
                                            <input type="text"
                                                   id="search-query"
                                                   name="query"
                                                   class="search-input"
                                                   placeholder="Massage, facial, manicure..."
                                                   value="{{ request.GET.query|default:'' }}"
                                                   autocomplete="off">
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Location Search Field -->
                            <div class="col-lg-4 col-md-6">
                                <div class="search-form-group">
                                    <label class="search-label" for="search-location">Where?</label>
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-map-marker-alt search-icon"></i>
                                        {% if search_form %}
                                            {{ search_form.location }}
                                        {% else %}
                                            <input type="text"
                                                   id="search-location"
                                                   name="location"
                                                   class="search-input"
                                                   placeholder="City, state, or zip code"
                                                   value="{{ request.GET.location|default:'' }}"
                                                   autocomplete="off">
                                        {% endif %}
                                        <div class="location-actions">
                                            <button type="button" class="location-btn" onclick="getCurrentLocation()" title="Use current location">
                                                <i class="fas fa-crosshairs"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Search Button -->
                            <div class="col-lg-3 col-md-12">
                                <div class="search-form-group">
                                    <label class="search-label" style="opacity: 0; pointer-events: none;">Search</label>
                                    <button type="submit" class="search-btn">
                                        <i class="fas fa-search me-2"></i>
                                        Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            <!-- Booking Counter -->
                <div class="booking-count">
                    <strong>425,731</strong> appointments booked today
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            // For demo purposes, just show a message
            // In a real implementation, you would reverse geocode the coordinates
            const locationInput = document.getElementById('search-location');
            locationInput.value = 'Current location detected...';
            locationInput.placeholder = 'Getting your location...';

            // Simulate location detection
            setTimeout(() => {
                locationInput.value = '';
                locationInput.placeholder = 'City, state, or zip code';
                alert('Location detection would be implemented here with reverse geocoding service.');
            }, 1500);
        }, function(error) {
            alert('Unable to detect your location. Please enter your location manually.');
        });
    } else {
        alert('Geolocation is not supported by this browser.');
    }
}
</script>
{% endblock %}

{% block content %}
<!-- Popular Categories Section -->
<section class="popular-categories">
    <div class="container">
        <h2 class="section-title">Popular Categories</h2>
        <p class="section-subtitle">Discover the most sought-after spa and wellness services</p>

        <div class="row g-4">
            {% for category in popular_categories %}
            <div class="col-lg-2 col-md-4 col-sm-6 col-6">
                <a href="{{ category.get_absolute_url }}" class="category-card d-block">
                    <div class="category-icon">
                        {% if category.category_name == 'Spa' %}
                            <i class="fas fa-spa"></i>
                        {% elif category.category_name == 'Beauty Salon' %}
                            <i class="fas fa-cut"></i>
                        {% elif category.category_name == 'Massage' %}
                            <i class="fas fa-hand-paper"></i>
                        {% elif category.category_name == 'Fitness & Wellness' %}
                            <i class="fas fa-dumbbell"></i>
                        {% elif category.category_name == 'Yoga Studio' %}
                            <i class="fas fa-leaf"></i>
                        {% elif category.category_name == 'Medical Spa' %}
                            <i class="fas fa-stethoscope"></i>
                        {% elif category.category_name == 'Day Spa' %}
                            <i class="fas fa-sun"></i>
                        {% elif category.category_name == 'Wellness Center' %}
                            <i class="fas fa-heart"></i>
                        {% else %}
                            <i class="fas fa-spa"></i>
                        {% endif %}
                    </div>
                    <div class="category-name">{{ category.category_name }}</div>
                    <div class="category-count">{{ category.venue_count }} venue{{ category.venue_count|pluralize }}</div>
                </a>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-muted">No categories available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Service Highlights Section -->
<section class="service-highlights">
    <div class="container">
        <h2 class="section-title">Popular Services</h2>
        <p class="section-subtitle">Book the most popular spa and wellness treatments</p>

        <div class="row g-4">
            {% for service in popular_service_types %}
            <div class="col-lg-4 col-md-6">
                <a href="{% url 'venues_app:venue_search' %}?query={{ service.search_query }}" class="service-card-highlight d-block">
                    <div class="service-icon">
                        <i class="{{ service.icon }}"></i>
                    </div>
                    <div class="service-name">{{ service.name }}</div>
                    <div class="service-description">{{ service.description }}</div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Top Venues Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Top Venues</h2>
            <p class="section-subtitle">Discover the highest-rated venues in your area</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in top_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No top venues available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trending Venues Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Trending Now</h2>
            <p class="section-subtitle">Popular venues with recent activity and great reviews</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in trending_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No trending venues available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hot Deals Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Hot Deals</h2>
            <p class="section-subtitle">Limited-time offers and special promotions</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in hot_deals_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No special deals available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

{% if not user.is_customer %}
<!-- Business Section -->
<section class="business-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="business-title">CozyWish<br><span class="text-neutral-cw">For Business</span></h2>
                <p class="business-description">
                    Supercharge your business for free with the world's top booking platform for salons and spas. Independently voted no. 1 by industry professionals.
                </p>
                <div class="d-flex flex-column flex-sm-row gap-3 mb-4">
                    <a href="{% url 'accounts_app:for_business' %}" class="btn-cw-primary">
                        <i class="fas fa-spa"></i>Find out more
                    </a>
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-secondary">
                        <i class="fas fa-user-plus"></i>Get Started
                    </a>
                </div>
                <div class="rating-container">
                    <h4 class="rating-title">Excellent 5/5</h4>
                    <div class="star-rating mb-2">
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                    </div>
                    <div class="review-text">
                        Over 1250 reviews from our partners
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="app-preview">
                    <img src="https://via.placeholder.com/500x300/fae1d7/2F160F?text=CozyWish+App" alt="CozyWish App Interface" class="app-interface">
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
