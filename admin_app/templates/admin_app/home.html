{% extends 'base.html' %}

{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}

{% block extra_css %}
<!-- Additional Google Fonts for Display Typography -->
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Yeseva+One&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Home Page */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #FFF9F4;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Hero Section */
    .hero-title {
        font-family: var(--cw-font-display);
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(47, 22, 15, 0.1);
    }

    .hero-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2.5rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Search Container */
    .search-container {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 1rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        border: 2px solid var(--cw-brand-accent);
    }

    .search-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
    }

    .search-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .input-icon-left {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--cw-brand-primary);
        z-index: 3;
        font-size: 1rem;
    }

    .search-btn {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        color: white;
        padding: 1rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
        width: 100%;
    }

    .search-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-lg);
    }

    .search-divider {
        width: 1px;
        background: var(--cw-brand-accent);
        margin: 0 1rem;
    }

    .booking-count {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        margin-top: 1.5rem;
    }

    .booking-count strong {
        color: var(--cw-brand-primary);
        font-weight: 700;
    }

    /* Section Styling */
    .popular-categories {
        padding: 5rem 0;
        background: var(--cw-accent-light);
    }

    .service-highlights {
        padding: 5rem 0;
        background: white;
    }

    .section-sm {
        padding: 4rem 0;
        background: var(--cw-accent-light);
    }

    .section-sm:nth-child(even) {
        background: white;
    }

    .section-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        text-align: center;
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        text-align: center;
        margin-bottom: 3rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Category Cards */
    .category-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        background: var(--cw-gradient-card-subtle);
    }

    .category-icon {
        font-size: 2.5rem;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: block;
    }

    .category-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .category-count {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Service Cards */
    .service-card-highlight {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-sm);
    }

    .service-card-highlight:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        background: var(--cw-gradient-card-subtle);
    }

    .service-icon {
        font-size: 2.5rem;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: block;
    }

    .service-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-family: var(--cw-font-heading);
    }

    .service-description {
        font-size: 1rem;
        color: var(--cw-neutral-600);
        line-height: 1.6;
    }

    /* Venue Cards */
    .service-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
        height: 100%;
    }

    .service-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
    }

    .service-card .card-img-top {
        height: 200px;
        object-fit: cover;
        border: none;
    }

    .service-card .card-body {
        padding: 1.5rem;
    }

    .service-card .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-family: var(--cw-font-heading);
    }

    .service-card .card-title a {
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    .service-card .card-title a:hover {
        color: var(--cw-brand-light);
    }

    .rating {
        margin-bottom: 0.5rem;
    }

    .rating-score {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .location {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .business-type {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-block;
    }

    /* Business Section */
    .business-section {
        padding: 5rem 0;
        background: var(--cw-gradient-card-subtle);
        position: relative;
        overflow: hidden;
    }

    .business-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="business-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23business-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .business-section .container {
        position: relative;
        z-index: 2;
    }

    .business-title {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .business-description {
        font-size: 1.125rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        color: white;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        color: var(--cw-brand-primary);
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .rating-container {
        margin-top: 2rem;
    }

    .rating-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .star-rating {
        margin-bottom: 0.75rem;
    }

    .star-rating .star {
        color: #FFD700;
        font-size: 1.25rem;
        margin-right: 0.25rem;
    }

    .review-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Dropdown Styling */
    .dropdown-menu {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 0.5rem 0;
    }

    .dropdown-item {
        color: var(--cw-neutral-700);
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
    }

    .dropdown-item:hover, .dropdown-item:focus {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .dropdown-item i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
        width: 1rem;
    }

    .dropdown-header {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .business-title {
            font-size: 2.25rem;
        }

        .category-card, .service-card-highlight {
            padding: 1.5rem 1rem;
        }

        .category-icon, .service-icon {
            font-size: 2rem;
        }

        .search-container {
            padding: 0.75rem;
        }

        .search-input {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
            font-size: 0.875rem;
        }

        .input-icon-left {
            left: 0.75rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .hero-title {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.75rem;
        }

        .business-title {
            font-size: 1.875rem;
        }

        .category-card, .service-card-highlight {
            padding: 1.25rem 0.75rem;
        }
    }
</style>
{% endblock %}

{% block hero_content %}
<!-- Hero section with gradient background -->
<div class="container mt-5" style="padding-top: 140px; padding-bottom: 80px;">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 text-center">
            <h1 class="hero-title">Find & Book<br>Local Spa and Massage Services</h1>
            <p class="hero-subtitle">Discover top-rated wellness venues and book your perfect relaxation experience</p>

            <!-- Search Form -->
            <form action="{% url 'venues_app:venue_search' %}" method="get">
                <div class="search-container">
                    <div class="row g-0 align-items-center">
                        <div class="col position-relative">
                            <div class="dropdown search-group">
                                <i class="fas fa-search input-icon-left"></i>
                                <input type="text" name="query" class="search-input form-control ps-5" data-bs-toggle="dropdown" placeholder="Search treatments..." aria-expanded="false" value="">
                                <input type="hidden" name="category" value="">
                                <div class="dropdown-menu treatment-dropdown">
                                    <a class="dropdown-item" href="#" data-category="">
                                        <i class="fas fa-th"></i>
                                        All treatments
                                    </a>
                                    <h6 class="dropdown-header">Top categories</h6>
                                    {% for category in categories %}
                                    <a class="dropdown-item" href="#" data-category="{{ category.id }}">
                                        {% if category.category_name == 'Spa' %}
                                            <i class="fas fa-spa"></i>
                                        {% elif category.category_name == 'Beauty Salon' %}
                                            <i class="fas fa-cut"></i>
                                        {% elif category.category_name == 'Massage' %}
                                            <i class="fas fa-hand-paper"></i>
                                        {% elif category.category_name == 'Fitness & Wellness' %}
                                            <i class="fas fa-dumbbell"></i>
                                        {% elif category.category_name == 'Yoga Studio' %}
                                            <i class="fas fa-leaf"></i>
                                        {% elif category.category_name == 'Medical Spa' %}
                                            <i class="fas fa-stethoscope"></i>
                                        {% elif category.category_name == 'Day Spa' %}
                                            <i class="fas fa-sun"></i>
                                        {% elif category.category_name == 'Wellness Center' %}
                                            <i class="fas fa-heart"></i>
                                        {% else %}
                                            <i class="fas fa-spa"></i>
                                        {% endif %}
                                        {{ category.category_name }}
                                    </a>
                                    {% empty %}
                                    <div class="dropdown-item-text text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        No categories available
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="search-divider"></div>
                        <div class="col position-relative">
                            <div class="dropdown search-group">
                                <i class="fas fa-map-marker-alt input-icon-left"></i>
                                <input type="text" name="location" id="id_location" class="search-input form-control ps-5" data-bs-toggle="dropdown" placeholder="City, State, or County" aria-expanded="false" value="" list="locationList" autocomplete="off">
                                <datalist id="locationList"></datalist>
                                <div class="dropdown-menu location-dropdown">
                                    <a class="dropdown-item" href="#" id="use-current-location">
                                        <i class="fas fa-location-arrow"></i>
                                        Use current location
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="search-btn">Search</button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Booking Counter -->
            <div class="booking-count">
                <strong>425,731</strong> appointments booked today
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Popular Categories Section -->
<section class="popular-categories">
    <div class="container">
        <h2 class="section-title">Popular Categories</h2>
        <p class="section-subtitle">Discover the most sought-after spa and wellness services</p>

        <div class="row g-4">
            {% for category in popular_categories %}
            <div class="col-lg-2 col-md-4 col-sm-6 col-6">
                <a href="{{ category.get_absolute_url }}" class="category-card d-block">
                    <div class="category-icon">
                        {% if category.category_name == 'Spa' %}
                            <i class="fas fa-spa"></i>
                        {% elif category.category_name == 'Beauty Salon' %}
                            <i class="fas fa-cut"></i>
                        {% elif category.category_name == 'Massage' %}
                            <i class="fas fa-hand-paper"></i>
                        {% elif category.category_name == 'Fitness & Wellness' %}
                            <i class="fas fa-dumbbell"></i>
                        {% elif category.category_name == 'Yoga Studio' %}
                            <i class="fas fa-leaf"></i>
                        {% elif category.category_name == 'Medical Spa' %}
                            <i class="fas fa-stethoscope"></i>
                        {% elif category.category_name == 'Day Spa' %}
                            <i class="fas fa-sun"></i>
                        {% elif category.category_name == 'Wellness Center' %}
                            <i class="fas fa-heart"></i>
                        {% else %}
                            <i class="fas fa-spa"></i>
                        {% endif %}
                    </div>
                    <div class="category-name">{{ category.category_name }}</div>
                    <div class="category-count">{{ category.venue_count }} venue{{ category.venue_count|pluralize }}</div>
                </a>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-muted">No categories available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Service Highlights Section -->
<section class="service-highlights">
    <div class="container">
        <h2 class="section-title">Popular Services</h2>
        <p class="section-subtitle">Book the most popular spa and wellness treatments</p>

        <div class="row g-4">
            {% for service in popular_service_types %}
            <div class="col-lg-4 col-md-6">
                <a href="{% url 'venues_app:venue_search' %}?query={{ service.search_query }}" class="service-card-highlight d-block">
                    <div class="service-icon">
                        <i class="{{ service.icon }}"></i>
                    </div>
                    <div class="service-name">{{ service.name }}</div>
                    <div class="service-description">{{ service.description }}</div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Top Venues Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Top Venues</h2>
            <p class="section-subtitle">Discover the highest-rated venues in your area</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in top_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No top venues available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trending Venues Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Trending Now</h2>
            <p class="section-subtitle">Popular venues with recent activity and great reviews</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in trending_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No trending venues available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hot Deals Section -->
<section class="section-sm">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Hot Deals</h2>
            <p class="section-subtitle">Limited-time offers and special promotions</p>
        </div>

        <div class="position-relative card-slider-container">
            <div class="card-slider">
                <div class="card-set row g-4 justify-content-center">
                    {% for venue in hot_deals_venues %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="card service-card">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none">
                                        {{ venue.venue_name }}
                                    </a>
                                </h5>
                                <div class="rating">
                                    <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                                    <span class="review-count">({{ venue.review_count|default:0 }})</span>
                                </div>
                                <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                                <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No special deals available at the moment.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

{% if not user.is_customer %}
<!-- Business Section -->
<section class="business-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="business-title">CozyWish<br><span class="text-neutral-cw">For Business</span></h2>
                <p class="business-description">
                    Supercharge your business for free with the world's top booking platform for salons and spas. Independently voted no. 1 by industry professionals.
                </p>
                <div class="d-flex flex-column flex-sm-row gap-3 mb-4">
                    <a href="{% url 'accounts_app:for_business' %}" class="btn-cw-primary">
                        <i class="fas fa-spa"></i>Find out more
                    </a>
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-secondary">
                        <i class="fas fa-user-plus"></i>Get Started
                    </a>
                </div>
                <div class="rating-container">
                    <h4 class="rating-title">Excellent 5/5</h4>
                    <div class="star-rating mb-2">
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                    </div>
                    <div class="review-text">
                        Over 1250 reviews from our partners
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="app-preview">
                    <img src="https://via.placeholder.com/500x300/fae1d7/2F160F?text=CozyWish+App" alt="CozyWish App Interface" class="app-interface">
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
